/**
 * HTML解析和数据提取模块
 * 
 * 提供豆瓣电影详情页面的HTML解析功能，提取电影的各项信息
 */

import { MovieInfo, DoubanScraperError, ErrorType } from '../types.ts';
import { fetchText } from './http.ts';

/**
 * 电影信息解析器类
 */
export class MovieParser {
  private parser = new DOMParser();

  /**
   * 解析电影信息
   * @param html 电影详情页面HTML
   * @returns MovieInfo 电影信息对象
   */
  parseMovieInfo(html: string): MovieInfo {
    try {
      const doc = this.parser.parseFromString(html, 'text/html');
      
      if (!doc) {
        throw new Error('HTML解析失败');
      }

      const title = this.extractTitle(doc);
      const year = this.extractYear(doc);
      const rating = this.extractRating(doc);
      const genres = this.extractGenres(doc);
      const actors = this.extractActors(doc);
      const poster = this.extractPoster(doc);

      return {
        title,
        year,
        rating,
        genres,
        actors,
        poster
      };
    } catch (error) {
      throw new DoubanScraperError(
        ErrorType.PARSE_ERROR,
        `解析电影信息失败: ${error.message}`,
        error
      );
    }
  }

  /**
   * 提取电影标题
   * @param doc HTML文档对象
   * @returns string 电影标题
   */
  private extractTitle(doc: Document): string {
    // 尝试多种选择器策略
    const selectors = [
      'h1 span[property="v:itemreviewed"]',
      'h1 span:first-child',
      'h1',
      '.subject h1',
      '[property="v:itemreviewed"]'
    ];

    for (const selector of selectors) {
      const element = doc.querySelector(selector);
      if (element?.textContent) {
        let title = element.textContent.trim();
        // 移除年份信息（如果包含在标题中）
        title = title.replace(/\s*\(\d{4}\)\s*$/, '');
        if (title) {
          return title;
        }
      }
    }

    throw new Error('无法提取电影标题');
  }

  /**
   * 提取上映年份
   * @param doc HTML文档对象
   * @returns string 上映年份
   */
  private extractYear(doc: Document): string {
    // 尝试从标题中提取年份
    const titleElement = doc.querySelector('h1');
    if (titleElement?.textContent) {
      const yearMatch = titleElement.textContent.match(/\((\d{4})\)/);
      if (yearMatch) {
        return yearMatch[1];
      }
    }

    // 尝试从上映日期中提取年份
    const releaseDateElements = doc.querySelectorAll('[property="v:initialReleaseDate"]');
    for (const element of releaseDateElements) {
      const dateText = element.textContent?.trim();
      if (dateText) {
        const yearMatch = dateText.match(/(\d{4})/);
        if (yearMatch) {
          return yearMatch[1];
        }
      }
    }

    // 从页面信息中查找年份
    const infoElements = doc.querySelectorAll('#info span');
    for (const element of infoElements) {
      const text = element.textContent?.trim();
      if (text && /^\d{4}$/.test(text)) {
        return text;
      }
    }

    return '未知';
  }

  /**
   * 提取豆瓣评分
   * @param doc HTML文档对象
   * @returns string 豆瓣评分
   */
  private extractRating(doc: Document): string {
    const selectors = [
      '.rating_num',
      '[property="v:average"]',
      '.ll.rating_num',
      'strong.ll.rating_num'
    ];

    for (const selector of selectors) {
      const element = doc.querySelector(selector);
      if (element?.textContent) {
        const rating = element.textContent.trim();
        if (rating && /^\d+(\.\d+)?$/.test(rating)) {
          return rating;
        }
      }
    }

    return '暂无评分';
  }

  /**
   * 提取电影类型
   * @param doc HTML文档对象
   * @returns string[] 电影类型列表
   */
  private extractGenres(doc: Document): string[] {
    const genres: string[] = [];

    // 尝试从属性中提取
    const genreElements = doc.querySelectorAll('[property="v:genre"]');
    for (const element of genreElements) {
      const genre = element.textContent?.trim();
      if (genre) {
        genres.push(genre);
      }
    }

    // 如果没有找到，尝试从信息区域提取
    if (genres.length === 0) {
      const infoText = doc.querySelector('#info')?.textContent || '';
      const genreMatch = infoText.match(/类型:\s*([^\n]+)/);
      if (genreMatch) {
        const genreText = genreMatch[1].trim();
        const extractedGenres = genreText.split(/[\/\s]+/).filter(g => g.trim());
        genres.push(...extractedGenres);
      }
    }

    return genres.length > 0 ? genres : ['未知'];
  }

  /**
   * 提取主演信息
   * @param doc HTML文档对象
   * @returns string[] 主演列表
   */
  private extractActors(doc: Document): string[] {
    const actors: string[] = [];

    // 尝试从演职员列表提取
    const actorElements = doc.querySelectorAll('.actor a, .celebrities-list .celebrity .name a');
    for (const element of actorElements) {
      const actor = element.textContent?.trim();
      if (actor) {
        actors.push(actor);
      }
    }

    // 如果没有找到，尝试从信息区域提取
    if (actors.length === 0) {
      const infoText = doc.querySelector('#info')?.textContent || '';
      const actorMatch = infoText.match(/主演:\s*([^\n]+)/);
      if (actorMatch) {
        const actorText = actorMatch[1].trim();
        const extractedActors = actorText.split(/[\/\s]+/).filter(a => a.trim());
        actors.push(...extractedActors);
      }
    }

    return actors.length > 0 ? actors : ['未知'];
  }

  /**
   * 提取电影封面
   * @param doc HTML文档对象
   * @returns string 封面图片URL
   */
  private extractPoster(doc: Document): string {
    const selectors = [
      '#mainpic img',
      '.subject img',
      'img[rel="v:image"]',
      '.pic img',
      'img[src*="img.douban.com"]'
    ];

    for (const selector of selectors) {
      const element = doc.querySelector(selector) as HTMLImageElement;
      if (element?.src) {
        let src = element.src;
        // 确保使用高质量图片
        src = src.replace(/\/s_ratio_poster\//, '/l_ratio_poster/');
        src = src.replace(/\/m_ratio_poster\//, '/l_ratio_poster/');
        return src;
      }
    }

    return '暂无封面';
  }
}

/**
 * 获取并解析电影信息
 * @param movieUrl 电影详情页URL
 * @returns Promise<MovieInfo> 电影信息
 */
export async function getMovieInfo(movieUrl: string): Promise<MovieInfo> {
  try {
    console.log(`📄 获取电影详情: ${movieUrl}`);
    
    const html = await fetchText(movieUrl);
    const parser = new MovieParser();
    const movieInfo = parser.parseMovieInfo(html);
    
    console.log(`✅ 成功解析电影信息: ${movieInfo.title}`);
    return movieInfo;
    
  } catch (error) {
    if (error instanceof DoubanScraperError) {
      throw error;
    }
    throw new DoubanScraperError(
      ErrorType.PARSE_ERROR,
      `获取电影信息失败: ${error.message}`,
      error
    );
  }
}

/**
 * 创建默认的电影解析器实例
 */
export const movieParser = new MovieParser();
