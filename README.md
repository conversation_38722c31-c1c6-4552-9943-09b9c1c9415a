# 豆瓣电影爬虫 🎬

一个使用 Deno 开发的豆瓣电影信息抓取工具，支持通过电影名称搜索并获取详细的电影信息。

## 功能特性

- 🔍 **智能搜索**: 支持中文电影名称搜索
- 📊 **完整信息**: 获取电影标题、年份、评分、类型、主演、封面等信息
- 🎨 **美观输出**: 支持彩色终端输出和多种格式
- 🚀 **高性能**: 内置重试机制和错误处理
- 🛡️ **反爬虫**: 模拟真实浏览器请求头
- 📱 **易用性**: 简单的命令行界面

## 系统要求

- [Deno](https://deno.land/) 1.40.0 或更高版本
- 网络连接

## 安装和使用

### 1. 克隆项目

```bash
git clone <repository-url>
cd douban-movie-scraper
```

### 2. 运行程序

#### 命令行模式

```bash
# 基本用法
deno run --allow-net --allow-env main.ts -m "阳光普照"

# 指定输出格式
deno run --allow-net --allow-env main.ts --movie "82年生的金智英" --format json

# 查看帮助信息
deno run --allow-net --allow-env main.ts --help
```

#### Web界面模式

```bash
# 启动Web服务器
deno run --allow-net --allow-env --allow-read server.ts

# 或使用任务命令
deno task web

# 然后在浏览器中访问
# http://localhost:8000
```

### 3. 命令行参数

| 参数 | 简写 | 说明 | 示例 |
|------|------|------|------|
| `--movie` | `-m` | 要搜索的电影名称 | `-m "阳光普照"` |
| `--format` | `-f` | 输出格式 (text/json) | `-f json` |
| `--help` | `-h` | 显示帮助信息 | `-h` |
| `--version` | `-v` | 显示版本信息 | `-v` |

## 输出示例

### 文本格式输出

```
🎬 豆瓣电影爬虫 v1.0.0
==================================================

🔍 正在搜索电影: "阳光普照"
✅ 找到 3 个搜索结果
📍 选择电影: 阳光普照 A Sun

📄 正在获取电影详情...
✅ 成功解析电影信息: 阳光普照

🎯 电影信息获取成功！

电影《阳光普照》 (2019)
豆瓣：8.6
类型：剧情/家庭/犯罪
主演：陈以文/柯淑勤/巫建和/刘冠廷/许光汉
封面：https://img.douban.com/view/photo/l_ratio_poster/public/p2570570879.jpg

✅ 程序执行完成！
```

### JSON格式输出

```json
{
  "title": "阳光普照",
  "year": "2019",
  "rating": "8.6",
  "genres": ["剧情", "家庭", "犯罪"],
  "actors": ["陈以文", "柯淑勤", "巫建和", "刘冠廷", "许光汉"],
  "poster": "https://img.douban.com/view/photo/l_ratio_poster/public/p2570570879.jpg"
}
```

## 项目结构

```
douban-movie-scraper/
├── main.ts                 # 主程序入口（命令行模式）
├── server.ts               # Web服务器入口
├── types.ts                # 类型定义
├── deno.json              # Deno 配置文件
├── README.md              # 项目说明
├── modules/               # 核心功能模块
│   ├── cli.ts             # 命令行参数处理
│   ├── http.ts            # HTTP 请求工具
│   ├── search.ts          # 豆瓣搜索功能
│   ├── parser.ts          # HTML 解析和数据提取
│   └── formatter.ts       # 数据格式化输出
└── web/                   # Web前端文件
    ├── index.html         # 主页面
    ├── style.css          # 样式文件
    └── script.js          # JavaScript逻辑
```

## 技术特点

### 轻量级架构
- 使用 Deno 原生 API，无外部依赖
- 基于 fetch API 和 DOMParser
- TypeScript 类型安全

### 智能搜索
- 自动处理中文字符编码
- 智能匹配最佳搜索结果
- 支持模糊搜索和精确匹配

### 稳定可靠
- 内置重试机制和超时控制
- 完善的错误处理和用户提示
- 反爬虫机制规避

### 用户友好
- 彩色终端输出
- 详细的进度提示
- 多种输出格式支持

## 开发和调试

### 开发模式

```bash
# 启用调试模式
DEBUG=1 deno run --allow-net main.ts -m "电影名称"

# 代码格式化
deno task fmt

# 代码检查
deno task lint
```

### 测试

```bash
# 运行测试
deno task test
```

## 常见问题

### Q: 搜索不到电影怎么办？
A: 请检查电影名称是否正确，建议使用中文名称或尝试更简短的关键词。

### Q: 网络请求失败怎么办？
A: 请检查网络连接，确保可以访问豆瓣网站，必要时可以稍后重试。

### Q: 解析失败怎么办？
A: 可能是豆瓣网站更新了页面结构，请联系开发者更新程序。

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

## 免责声明

本工具仅供学习和研究使用，请遵守豆瓣网站的使用条款和相关法律法规。
