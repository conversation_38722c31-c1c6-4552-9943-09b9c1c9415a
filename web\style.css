/* 豆瓣电影搜索 - 样式文件 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* 主要内容区域 */
.main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* 搜索区域 */
.search-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.search-box {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

#movieInput {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

#movieInput:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

#searchBtn {
    padding: 15px 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

#searchBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

#searchBtn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.search-tips {
    text-align: center;
    color: #666;
    font-size: 0.9rem;
}

/* 结果区域 */
.result-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.movie-card {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.movie-poster {
    flex-shrink: 0;
}

.movie-poster img {
    width: 200px;
    height: 300px;
    object-fit: cover;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.movie-info {
    flex: 1;
}

.movie-title {
    font-size: 2rem;
    color: #333;
    margin-bottom: 20px;
    line-height: 1.3;
}

.movie-details {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.detail-item {
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.label {
    font-weight: 600;
    color: #555;
    min-width: 80px;
}

.rating {
    font-weight: 700;
    font-size: 1.2rem;
    color: #f39c12;
}

/* 错误区域 */
.error-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.error-card {
    text-align: center;
}

.error-icon {
    font-size: 3rem;
    margin-bottom: 20px;
}

.error-card h3 {
    color: #e74c3c;
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.error-card p {
    color: #666;
    margin-bottom: 20px;
    font-size: 1.1rem;
}

.error-suggestions {
    text-align: left;
    max-width: 400px;
    margin: 0 auto;
}

.error-suggestions h4 {
    color: #555;
    margin-bottom: 10px;
}

.error-suggestions ul {
    list-style: none;
    padding-left: 0;
}

.error-suggestions li {
    padding: 5px 0;
    color: #666;
}

.error-suggestions li:before {
    content: "• ";
    color: #667eea;
    font-weight: bold;
}

/* 加载区域 */
.loading-section {
    background: white;
    border-radius: 15px;
    padding: 50px 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    text-align: center;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-card p {
    color: #666;
    font-size: 1.1rem;
}

/* 底部样式 */
.footer {
    text-align: center;
    margin-top: 40px;
    color: white;
    opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .search-box {
        flex-direction: column;
    }
    
    .movie-card {
        flex-direction: column;
        text-align: center;
    }
    
    .movie-poster img {
        width: 150px;
        height: 225px;
        margin: 0 auto;
    }
    
    .movie-title {
        font-size: 1.5rem;
    }
    
    .detail-item {
        flex-direction: column;
        text-align: left;
    }
    
    .label {
        min-width: auto;
    }
}
