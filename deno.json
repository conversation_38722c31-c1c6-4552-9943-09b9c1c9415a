{"compilerOptions": {"allowJs": true, "lib": ["deno.window", "dom"], "strict": true}, "lint": {"rules": {"tags": ["recommended"]}}, "fmt": {"useTabs": false, "lineWidth": 80, "indentWidth": 2, "semiColons": true, "singleQuote": true, "proseWrap": "preserve"}, "imports": {"@std/cli": "jsr:@std/cli@^1.0.0", "@std/assert": "jsr:@std/assert@^1.0.0"}, "tasks": {"dev": "deno run --allow-net --allow-read main.ts", "start": "deno run --allow-net --allow-read main.ts", "test": "deno test --allow-net --allow-read", "lint": "deno lint", "fmt": "deno fmt"}, "exclude": ["node_modules", "dist"]}