{"compilerOptions": {"lib": ["deno.window", "dom"], "strict": true}, "lint": {"rules": {"tags": ["recommended"]}}, "fmt": {"useTabs": false, "lineWidth": 80, "indentWidth": 2, "semiColons": true, "singleQuote": true, "proseWrap": "preserve"}, "imports": {"@std/cli": "jsr:@std/cli@^1.0.0", "@std/assert": "jsr:@std/assert@^1.0.0"}, "tasks": {"dev": "deno run --allow-net --allow-env main.ts", "start": "deno run --allow-net --allow-env main.ts", "server": "deno run --allow-net --allow-env --allow-read server.ts", "web": "deno run --allow-net --allow-env --allow-read server.ts", "test": "deno test --allow-net --allow-env", "lint": "deno lint", "fmt": "deno fmt"}, "exclude": ["node_modules", "dist"]}