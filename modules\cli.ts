/**
 * 命令行参数处理模块
 * 
 * 提供命令行参数解析、帮助信息显示、参数验证等功能
 */

import { parseArgs } from '@std/cli';
import { CliArgs, DoubanScraperError, ErrorType } from '../types.ts';

/**
 * 应用版本信息
 */
const VERSION = '1.0.0';

/**
 * 应用名称
 */
const APP_NAME = '豆瓣电影爬虫';

/**
 * 帮助信息
 */
const HELP_TEXT = `
${APP_NAME} v${VERSION}

用法:
  deno run --allow-net main.ts [选项]

选项:
  -m, --movie <名称>    要搜索的电影名称
  -f, --format <格式>   输出格式 (text|json) [默认: text]
  -h, --help           显示帮助信息
  -v, --version        显示版本信息

示例:
  deno run --allow-net main.ts -m "阳光普照"
  deno run --allow-net main.ts --movie "82年生的金智英" --format json
  deno run --allow-net main.ts --help

说明:
  该工具可以根据电影名称搜索豆瓣电影信息，包括评分、类型、主演等详细信息。
`;

/**
 * 解析命令行参数
 * @param args 命令行参数数组
 * @returns 解析后的参数对象
 */
export function parseCliArgs(args: string[]): CliArgs {
  try {
    const parsed = parseArgs(args, {
      string: ['movie', 'format'],
      boolean: ['help', 'version'],
      alias: {
        movie: 'm',
        format: 'f',
        help: 'h',
        version: 'v'
      },
      default: {
        format: 'text'
      }
    });

    return {
      movie: parsed.movie,
      help: parsed.help,
      format: parsed.format as 'text' | 'json'
    };
  } catch (error) {
    throw new DoubanScraperError(
      ErrorType.INVALID_ARGS,
      `参数解析失败: ${error.message}`,
      error
    );
  }
}

/**
 * 显示帮助信息
 */
export function showHelp(): void {
  console.log(HELP_TEXT);
}

/**
 * 显示版本信息
 */
export function showVersion(): void {
  console.log(`${APP_NAME} v${VERSION}`);
}

/**
 * 验证命令行参数
 * @param args 解析后的参数对象
 * @throws {DoubanScraperError} 当参数无效时抛出错误
 */
export function validateArgs(args: CliArgs): void {
  // 如果显示帮助或版本信息，则不需要验证其他参数
  if (args.help || args.version) {
    return;
  }

  // 检查是否提供了电影名称
  if (!args.movie || args.movie.trim() === '') {
    throw new DoubanScraperError(
      ErrorType.INVALID_ARGS,
      '请提供要搜索的电影名称。使用 -m 或 --movie 参数指定电影名称。'
    );
  }

  // 检查电影名称长度
  if (args.movie.trim().length < 1) {
    throw new DoubanScraperError(
      ErrorType.INVALID_ARGS,
      '电影名称不能为空。'
    );
  }

  if (args.movie.trim().length > 100) {
    throw new DoubanScraperError(
      ErrorType.INVALID_ARGS,
      '电影名称过长，请输入100个字符以内的名称。'
    );
  }

  // 检查输出格式
  if (args.format && !['text', 'json'].includes(args.format)) {
    throw new DoubanScraperError(
      ErrorType.INVALID_ARGS,
      '无效的输出格式。支持的格式: text, json'
    );
  }
}

/**
 * 处理命令行参数的主函数
 * @param args 命令行参数数组
 * @returns 处理后的参数对象，如果需要退出程序则返回null
 */
export function handleCliArgs(args: string[]): CliArgs | null {
  try {
    const parsedArgs = parseCliArgs(args);

    // 处理帮助信息
    if (parsedArgs.help) {
      showHelp();
      return null;
    }

    // 处理版本信息
    if (parsedArgs.version) {
      showVersion();
      return null;
    }

    // 验证参数
    validateArgs(parsedArgs);

    return parsedArgs;
  } catch (error) {
    if (error instanceof DoubanScraperError) {
      console.error(`❌ 错误: ${error.message}`);
      console.error('使用 --help 查看使用说明。');
    } else {
      console.error(`❌ 未知错误: ${error.message}`);
    }
    return null;
  }
}

/**
 * 显示错误信息并退出
 * @param message 错误信息
 * @param showHelpHint 是否显示帮助提示
 */
export function showError(message: string, showHelpHint = true): void {
  console.error(`❌ 错误: ${message}`);
  if (showHelpHint) {
    console.error('使用 --help 查看使用说明。');
  }
}
