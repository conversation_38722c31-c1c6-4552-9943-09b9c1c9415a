// 豆瓣电影搜索 - JavaScript 文件

// API 配置
const API_BASE_URL = 'http://localhost:8001';

// DOM 元素
const movieInput = document.getElementById('movieInput');
const searchBtn = document.getElementById('searchBtn');
const resultSection = document.getElementById('resultSection');
const errorSection = document.getElementById('errorSection');
const loadingSection = document.getElementById('loadingSection');

// 电影信息元素
const movieTitle = document.getElementById('movieTitle');
const movieRating = document.getElementById('movieRating');
const movieGenres = document.getElementById('movieGenres');
const movieActors = document.getElementById('movieActors');
const moviePoster = document.getElementById('moviePoster');
const movieSummary = document.getElementById('movieSummary');
const errorMessage = document.getElementById('errorMessage');

/**
 * 搜索电影
 */
async function searchMovie() {
    const movieName = movieInput.value.trim();
    
    if (!movieName) {
        showError('请输入电影名称');
        return;
    }
    
    // 显示加载状态
    showLoading();
    setButtonLoading(true);
    
    try {
        const response = await fetch(`${API_BASE_URL}/api/search`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                movieName: movieName
            })
        });
        
        const data = await response.json();

        // 调试日志
        console.log('API返回数据:', data);

        if (data.success) {
            console.log('电影数据:', data.data);
            showResult(data.data);
        } else {
            showError(data.error || '搜索失败');
        }
        
    } catch (error) {
        console.error('搜索错误:', error);
        showError('网络连接失败，请检查服务器是否启动');
    } finally {
        setButtonLoading(false);
    }
}

/**
 * 显示搜索结果
 * @param {Object} movieData 电影数据
 */
function showResult(movieData) {
    hideAllSections();
    
    // 设置电影信息
    movieTitle.textContent = `${movieData.title} (${movieData.year})`;
    movieRating.textContent = movieData.rating;
    movieGenres.textContent = movieData.genres.join(' / ');
    movieActors.textContent = movieData.actors.join(' / ');
    movieSummary.textContent = movieData.summary || '暂无简介';

    // 设置封面图片
    if (movieData.poster && movieData.poster !== '暂无封面') {
        moviePoster.src = movieData.poster;
        moviePoster.alt = `${movieData.title} 封面`;
    } else {
        moviePoster.src = getDefaultPosterUrl();
        moviePoster.alt = '暂无封面';
    }
    
    // 显示结果区域
    resultSection.style.display = 'block';
    
    // 滚动到结果区域
    resultSection.scrollIntoView({ behavior: 'smooth' });
}

/**
 * 显示错误信息
 * @param {string} message 错误消息
 */
function showError(message) {
    hideAllSections();
    
    errorMessage.textContent = message;
    errorSection.style.display = 'block';
    
    // 滚动到错误区域
    errorSection.scrollIntoView({ behavior: 'smooth' });
}

/**
 * 显示加载状态
 */
function showLoading() {
    hideAllSections();
    loadingSection.style.display = 'block';
    
    // 滚动到加载区域
    loadingSection.scrollIntoView({ behavior: 'smooth' });
}

/**
 * 隐藏所有结果区域
 */
function hideAllSections() {
    resultSection.style.display = 'none';
    errorSection.style.display = 'none';
    loadingSection.style.display = 'none';
}

/**
 * 设置按钮加载状态
 * @param {boolean} loading 是否加载中
 */
function setButtonLoading(loading) {
    const btnText = searchBtn.querySelector('.btn-text');
    const btnLoading = searchBtn.querySelector('.btn-loading');
    
    if (loading) {
        btnText.style.display = 'none';
        btnLoading.style.display = 'inline';
        searchBtn.disabled = true;
    } else {
        btnText.style.display = 'inline';
        btnLoading.style.display = 'none';
        searchBtn.disabled = false;
    }
}

/**
 * 获取默认封面图片URL
 * @returns {string} 默认封面图片的Data URL
 */
function getDefaultPosterUrl() {
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDIwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjZjVmNWY1Ii8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjEwMCIgcj0iMzAiIGZpbGw9IiNjY2MiLz4KPHJlY3QgeD0iNjAiIHk9IjE2MCIgd2lkdGg9IjgwIiBoZWlnaHQ9IjEwIiByeD0iNSIgZmlsbD0iI2NjYyIvPgo8cmVjdCB4PSI0MCIgeT0iMTgwIiB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEwIiByeD0iNSIgZmlsbD0iI2NjYyIvPgo8cmVjdCB4PSI3MCIgeT0iMjAwIiB3aWR0aD0iNjAiIGhlaWdodD0iMTAiIHJ4PSI1IiBmaWxsPSIjY2NjIi8+CjwvdGc+Cjwvc3ZnPgo=';
}

/**
 * 格式化评分显示
 * @param {string} rating 评分
 * @returns {string} 格式化后的评分
 */
function formatRating(rating) {
    if (rating === '暂无评分') {
        return rating;
    }
    
    const score = parseFloat(rating);
    if (isNaN(score)) {
        return rating;
    }
    
    // 添加星级显示
    const stars = Math.round(score / 2);
    const starDisplay = '★'.repeat(stars) + '☆'.repeat(5 - stars);
    
    return `${rating} ${starDisplay}`;
}

// 事件监听器
document.addEventListener('DOMContentLoaded', function() {
    // 回车键搜索
    movieInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchMovie();
        }
    });
    
    // 输入框焦点
    movieInput.addEventListener('focus', function() {
        hideAllSections();
    });
    
    // 示例电影名称
    const examples = ['阳光普照', '寄生虫', '82年生的金智英', '小丑', '1917'];
    let exampleIndex = 0;
    
    // 定期更换占位符文本
    setInterval(() => {
        movieInput.placeholder = `请输入电影名称，如：${examples[exampleIndex]}`;
        exampleIndex = (exampleIndex + 1) % examples.length;
    }, 3000);
});

// 全局错误处理
window.addEventListener('error', function(e) {
    console.error('页面错误:', e.error);
});

window.addEventListener('unhandledrejection', function(e) {
    console.error('未处理的Promise拒绝:', e.reason);
});
