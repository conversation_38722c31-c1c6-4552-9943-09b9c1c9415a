<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>豆瓣电影搜索</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🎬 豆瓣电影搜索</h1>
            <p class="subtitle">输入电影名称，获取详细信息</p>
        </header>

        <main class="main">
            <div class="search-section">
                <div class="search-box">
                    <input 
                        type="text" 
                        id="movieInput" 
                        placeholder="请输入电影名称，如：阳光普照"
                        autocomplete="off"
                    >
                    <button id="searchBtn" onclick="searchMovie()">
                        <span class="btn-text">搜索</span>
                        <span class="btn-loading" style="display: none;">搜索中...</span>
                    </button>
                </div>
                <div class="search-tips">
                    <p>💡 提示：支持中文电影名称搜索，如"阳光普照"、"寄生虫"等</p>
                </div>
            </div>

            <div class="result-section" id="resultSection" style="display: none;">
                <div class="movie-card" id="movieCard">
                    <div class="movie-poster">
                        <img id="moviePoster" src="" alt="电影封面" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDIwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0xMDAgMTUwQzEwNSAxNTAgMTEwIDE0NSAxMTAgMTQwQzExMCAxMzUgMTA1IDEzMCAxMDAgMTMwQzk1IDEzMCA5MCAxMzUgOTAgMTQwQzkwIDE0NSA5NSAxNTAgMTAwIDE1MFoiIGZpbGw9IiNjY2MiLz4KPHBhdGggZD0iTTEwMCAxNzBDMTEwIDE3MCAxMjAgMTYwIDEyMCAxNTBDMTIwIDE0MCAxMTAgMTMwIDEwMCAxMzBDOTAgMTMwIDgwIDE0MCA4MCAxNTBDODAgMTYwIDkwIDE3MCAxMDAgMTcwWiIgZmlsbD0iI2NjYyIvPgo8L3N2Zz4K'">
                    </div>
                    <div class="movie-info">
                        <h2 class="movie-title" id="movieTitle"></h2>
                        <div class="movie-details">
                            <div class="detail-item">
                                <span class="label">豆瓣评分：</span>
                                <span class="rating" id="movieRating"></span>
                            </div>
                            <div class="detail-item">
                                <span class="label">类型：</span>
                                <span id="movieGenres"></span>
                            </div>
                            <div class="detail-item">
                                <span class="label">主演：</span>
                                <span id="movieActors"></span>
                            </div>
                            <div class="detail-item summary-item">
                                <span class="label">简介：</span>
                                <div class="summary-content" id="movieSummary"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="error-section" id="errorSection" style="display: none;">
                <div class="error-card">
                    <div class="error-icon">❌</div>
                    <h3>搜索失败</h3>
                    <p id="errorMessage"></p>
                    <div class="error-suggestions">
                        <h4>建议：</h4>
                        <ul>
                            <li>检查电影名称是否正确</li>
                            <li>尝试使用电影的中文名称</li>
                            <li>尝试使用更简短的关键词</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="loading-section" id="loadingSection" style="display: none;">
                <div class="loading-card">
                    <div class="loading-spinner"></div>
                    <p>正在搜索电影信息...</p>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>© 2024 豆瓣电影搜索工具 | 仅供学习研究使用</p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>
