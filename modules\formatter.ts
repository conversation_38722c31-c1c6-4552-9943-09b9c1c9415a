/**
 * 数据格式化输出模块
 * 
 * 提供电影信息的格式化输出功能，支持多种输出格式和美观的显示效果
 */

import { MovieInfo, FormatOptions } from '../types.ts';

/**
 * 颜色代码常量
 */
const Colors = {
  RESET: '\x1b[0m',
  BRIGHT: '\x1b[1m',
  DIM: '\x1b[2m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m'
};

/**
 * 电影信息格式化器类
 */
export class MovieFormatter {
  /**
   * 格式化电影信息
   * @param movie 电影信息对象
   * @param options 格式化选项
   * @returns 格式化后的字符串
   */
  formatMovie(movie: MovieInfo, options: FormatOptions = { format: 'text' }): string {
    switch (options.format) {
      case 'json':
        return this.formatAsJson(movie, options);
      case 'text':
      default:
        return this.formatAsText(movie, options);
    }
  }

  /**
   * 格式化为文本格式
   * @param movie 电影信息对象
   * @param options 格式化选项
   * @returns 格式化后的文本
   */
  private formatAsText(movie: MovieInfo, options: FormatOptions): string {
    const { title, year, rating, genres, actors, poster } = movie;
    const colorize = options.colorize !== false; // 默认启用颜色
    
    const lines: string[] = [];
    
    // 电影标题和年份
    const titleLine = `电影《${title}》 (${year})`;
    lines.push(colorize ? this.colorize(titleLine, Colors.BRIGHT + Colors.CYAN) : titleLine);
    
    // 豆瓣评分
    const ratingLine = `豆瓣：${rating}`;
    const ratingColor = this.getRatingColor(rating);
    lines.push(colorize ? this.colorize(ratingLine, ratingColor) : ratingLine);
    
    // 电影类型
    const genresText = this.formatGenres(genres);
    const genresLine = `类型：${genresText}`;
    lines.push(colorize ? this.colorize(genresLine, Colors.YELLOW) : genresLine);
    
    // 主演
    const actorsText = this.formatActors(actors);
    const actorsLine = `主演：${actorsText}`;
    lines.push(colorize ? this.colorize(actorsLine, Colors.GREEN) : actorsLine);
    
    // 封面
    if (poster && poster !== '暂无封面') {
      const posterLine = `封面：${poster}`;
      lines.push(colorize ? this.colorize(posterLine, Colors.BLUE) : posterLine);
    }
    
    // 添加详细信息（如果启用）
    if (options.verbose) {
      lines.push('');
      lines.push(this.formatVerboseInfo(movie, colorize));
    }
    
    return lines.join('\n');
  }

  /**
   * 格式化为JSON格式
   * @param movie 电影信息对象
   * @param options 格式化选项
   * @returns 格式化后的JSON字符串
   */
  private formatAsJson(movie: MovieInfo, options: FormatOptions): string {
    const jsonData = {
      title: movie.title,
      year: movie.year,
      rating: movie.rating,
      genres: movie.genres,
      actors: movie.actors,
      poster: movie.poster
    };
    
    return JSON.stringify(jsonData, null, options.verbose ? 2 : 0);
  }

  /**
   * 格式化电影类型
   * @param genres 类型数组
   * @returns 格式化后的类型字符串
   */
  private formatGenres(genres: string[]): string {
    if (!genres || genres.length === 0 || (genres.length === 1 && genres[0] === '未知')) {
      return '未知';
    }
    return genres.join('/');
  }

  /**
   * 格式化主演信息
   * @param actors 主演数组
   * @returns 格式化后的主演字符串
   */
  private formatActors(actors: string[]): string {
    if (!actors || actors.length === 0 || (actors.length === 1 && actors[0] === '未知')) {
      return '未知';
    }
    
    // 限制显示的主演数量
    const maxActors = 5;
    if (actors.length > maxActors) {
      return actors.slice(0, maxActors).join('/') + '等';
    }
    
    return actors.join('/');
  }

  /**
   * 根据评分获取颜色
   * @param rating 评分字符串
   * @returns 颜色代码
   */
  private getRatingColor(rating: string): string {
    if (rating === '暂无评分') {
      return Colors.DIM;
    }
    
    const score = parseFloat(rating);
    if (isNaN(score)) {
      return Colors.DIM;
    }
    
    if (score >= 8.0) {
      return Colors.BRIGHT + Colors.GREEN;
    } else if (score >= 7.0) {
      return Colors.GREEN;
    } else if (score >= 6.0) {
      return Colors.YELLOW;
    } else {
      return Colors.RED;
    }
  }

  /**
   * 添加颜色
   * @param text 文本
   * @param color 颜色代码
   * @returns 带颜色的文本
   */
  private colorize(text: string, color: string): string {
    return `${color}${text}${Colors.RESET}`;
  }

  /**
   * 格式化详细信息
   * @param movie 电影信息对象
   * @param colorize 是否启用颜色
   * @returns 详细信息字符串
   */
  private formatVerboseInfo(movie: MovieInfo, colorize: boolean): string {
    const lines: string[] = [];
    
    lines.push(colorize ? this.colorize('详细信息:', Colors.BRIGHT) : '详细信息:');
    lines.push(`  标题: ${movie.title}`);
    lines.push(`  年份: ${movie.year}`);
    lines.push(`  评分: ${movie.rating}`);
    lines.push(`  类型数量: ${movie.genres.length}`);
    lines.push(`  主演数量: ${movie.actors.length}`);
    
    if (movie.poster && movie.poster !== '暂无封面') {
      lines.push(`  封面可用: 是`);
    } else {
      lines.push(`  封面可用: 否`);
    }
    
    return lines.join('\n');
  }
}

/**
 * 格式化电影信息（便捷函数）
 * @param movie 电影信息对象
 * @param format 输出格式
 * @param colorize 是否启用颜色
 * @returns 格式化后的字符串
 */
export function formatMovie(
  movie: MovieInfo, 
  format: 'text' | 'json' = 'text',
  colorize = true
): string {
  const formatter = new MovieFormatter();
  return formatter.formatMovie(movie, { format, colorize });
}

/**
 * 格式化电影信息为JSON
 * @param movie 电影信息对象
 * @param pretty 是否美化输出
 * @returns JSON字符串
 */
export function formatMovieAsJson(movie: MovieInfo, pretty = false): string {
  const formatter = new MovieFormatter();
  return formatter.formatMovie(movie, { format: 'json', verbose: pretty });
}

/**
 * 显示电影信息
 * @param movie 电影信息对象
 * @param options 格式化选项
 */
export function displayMovie(movie: MovieInfo, options: FormatOptions = { format: 'text' }): void {
  const formatter = new MovieFormatter();
  const output = formatter.formatMovie(movie, options);
  console.log('\n' + output + '\n');
}

/**
 * 显示成功消息
 * @param message 消息内容
 * @param colorize 是否启用颜色
 */
export function showSuccess(message: string, colorize = true): void {
  const text = `✅ ${message}`;
  if (colorize) {
    console.log(`${Colors.GREEN}${text}${Colors.RESET}`);
  } else {
    console.log(text);
  }
}

/**
 * 显示错误消息
 * @param message 错误消息
 * @param colorize 是否启用颜色
 */
export function showError(message: string, colorize = true): void {
  const text = `❌ ${message}`;
  if (colorize) {
    console.error(`${Colors.RED}${text}${Colors.RESET}`);
  } else {
    console.error(text);
  }
}

/**
 * 显示警告消息
 * @param message 警告消息
 * @param colorize 是否启用颜色
 */
export function showWarning(message: string, colorize = true): void {
  const text = `⚠️ ${message}`;
  if (colorize) {
    console.warn(`${Colors.YELLOW}${text}${Colors.RESET}`);
  } else {
    console.warn(text);
  }
}

/**
 * 创建默认的格式化器实例
 */
export const movieFormatter = new MovieFormatter();
