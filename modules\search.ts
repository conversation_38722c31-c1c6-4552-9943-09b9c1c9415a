/**
 * 豆瓣搜索功能模块
 * 
 * 提供豆瓣电影搜索功能，包括搜索URL构建、结果解析、数据提取等
 */

import { SearchResult, DoubanScraperError, ErrorType } from '../types.ts';
import { fetchText } from './http.ts';

/**
 * 豆瓣搜索配置
 */
const SEARCH_CONFIG = {
  baseUrl: 'https://www.douban.com/search',
  movieCategory: '1002', // 电影分类代码
  maxResults: 10 // 最大搜索结果数量
};

/**
 * 构建豆瓣搜索URL
 * @param movieName 电影名称
 * @returns 搜索URL
 */
export function buildSearchUrl(movieName: string): string {
  const encodedName = encodeURIComponent(movieName.trim());
  return `${SEARCH_CONFIG.baseUrl}?cat=${SEARCH_CONFIG.movieCategory}&q=${encodedName}`;
}

/**
 * 搜索豆瓣电影
 * @param movieName 电影名称
 * @returns Promise<SearchResult[]> 搜索结果列表
 */
export async function searchMovie(movieName: string): Promise<SearchResult[]> {
  if (!movieName || movieName.trim() === '') {
    throw new DoubanScraperError(
      ErrorType.INVALID_ARGS,
      '电影名称不能为空'
    );
  }

  try {
    const searchUrl = buildSearchUrl(movieName);
    console.log(`🔍 搜索电影: ${movieName}`);
    console.log(`📡 请求URL: ${searchUrl}`);
    
    const html = await fetchText(searchUrl);
    const results = parseSearchResults(html);
    
    if (results.length === 0) {
      throw new DoubanScraperError(
        ErrorType.NO_RESULTS,
        `未找到电影 "${movieName}" 的搜索结果`
      );
    }
    
    console.log(`✅ 找到 ${results.length} 个搜索结果`);
    return results;
    
  } catch (error) {
    if (error instanceof DoubanScraperError) {
      throw error;
    }
    throw new DoubanScraperError(
      ErrorType.NETWORK_ERROR,
      `搜索电影失败: ${error.message}`,
      error
    );
  }
}

/**
 * 解析搜索结果页面
 * @param html 搜索结果页面HTML
 * @returns SearchResult[] 解析后的搜索结果
 */
export function parseSearchResults(html: string): SearchResult[] {
  try {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    
    if (!doc) {
      throw new Error('HTML解析失败');
    }
    
    const results: SearchResult[] = [];
    
    // 查找搜索结果项
    const resultItems = doc.querySelectorAll('.result');
    
    for (const item of resultItems) {
      try {
        const result = parseSearchResultItem(item);
        if (result && isValidMovieResult(result)) {
          results.push(result);
          
          // 限制结果数量
          if (results.length >= SEARCH_CONFIG.maxResults) {
            break;
          }
        }
      } catch (error) {
        // 跳过解析失败的单个结果项
        console.warn(`⚠️ 跳过解析失败的搜索结果: ${error.message}`);
        continue;
      }
    }
    
    return results;
    
  } catch (error) {
    throw new DoubanScraperError(
      ErrorType.PARSE_ERROR,
      `解析搜索结果失败: ${error.message}`,
      error
    );
  }
}

/**
 * 解析单个搜索结果项
 * @param item 搜索结果DOM元素
 * @returns SearchResult | null
 */
function parseSearchResultItem(item: Element): SearchResult | null {
  // 查找电影链接
  const linkElement = item.querySelector('a[href*="/subject/"]');
  if (!linkElement) {
    return null;
  }
  
  const href = linkElement.getAttribute('href');
  if (!href) {
    return null;
  }
  
  // 提取电影ID
  const idMatch = href.match(/\/subject\/(\d+)/);
  if (!idMatch) {
    return null;
  }
  
  const id = idMatch[1];
  
  // 构建完整URL
  let url = href;
  if (href.startsWith('/')) {
    url = `https://movie.douban.com${href}`;
  } else if (!href.startsWith('http')) {
    url = `https://movie.douban.com/subject/${id}/`;
  }
  
  // 提取电影标题
  const titleElement = item.querySelector('.title a, .title-text, h3 a');
  let title = '';
  
  if (titleElement) {
    title = titleElement.textContent?.trim() || '';
    // 清理标题中的多余空白和特殊字符
    title = title.replace(/\s+/g, ' ').trim();
  }
  
  // 如果没有找到标题，尝试从链接文本获取
  if (!title && linkElement.textContent) {
    title = linkElement.textContent.trim();
  }
  
  if (!title) {
    return null;
  }
  
  return {
    id,
    url,
    title
  };
}

/**
 * 验证搜索结果是否为有效的电影结果
 * @param result 搜索结果
 * @returns boolean
 */
function isValidMovieResult(result: SearchResult): boolean {
  // 检查URL是否包含电影相关路径
  if (!result.url.includes('/subject/') && !result.url.includes('movie.douban.com')) {
    return false;
  }
  
  // 检查标题是否有效
  if (!result.title || result.title.length < 1) {
    return false;
  }
  
  // 检查ID是否为数字
  if (!/^\d+$/.test(result.id)) {
    return false;
  }
  
  return true;
}

/**
 * 选择最佳匹配的搜索结果
 * @param results 搜索结果列表
 * @param originalQuery 原始搜索词
 * @returns SearchResult 最佳匹配结果
 */
export function selectBestMatch(results: SearchResult[], originalQuery: string): SearchResult {
  if (results.length === 0) {
    throw new DoubanScraperError(
      ErrorType.NO_RESULTS,
      '没有可用的搜索结果'
    );
  }
  
  // 如果只有一个结果，直接返回
  if (results.length === 1) {
    return results[0];
  }
  
  // 寻找标题最匹配的结果
  const query = originalQuery.toLowerCase().trim();
  
  for (const result of results) {
    const title = result.title.toLowerCase();
    
    // 精确匹配
    if (title === query) {
      return result;
    }
    
    // 包含匹配
    if (title.includes(query) || query.includes(title)) {
      return result;
    }
  }
  
  // 如果没有找到匹配的，返回第一个结果
  return results[0];
}

/**
 * 显示搜索结果列表
 * @param results 搜索结果列表
 */
export function displaySearchResults(results: SearchResult[]): void {
  console.log('\n📋 搜索结果:');
  results.forEach((result, index) => {
    console.log(`${index + 1}. ${result.title}`);
    console.log(`   ID: ${result.id}`);
    console.log(`   URL: ${result.url}`);
    console.log('');
  });
}
